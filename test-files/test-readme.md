# Test Markdown File

This is a **test markdown file** for file upload testing.

## Features Being Tested

- [x] Markdown file upload
- [x] File preview functionality
- [x] File categorization
- [ ] Advanced markdown rendering

## Code Example

```javascript
function testFileUpload() {
  console.log("Testing file upload functionality");
  return true;
}
```

## Table Example

| Feature | Status | Notes |
|---------|--------|-------|
| Upload | ✅ | Working |
| Preview | ✅ | Working |
| Download | ✅ | Working |

## Links

- [GitHub](https://github.com)
- [Documentation](https://docs.example.com)

> This is a blockquote to test markdown rendering in the preview modal.

### Lists

1. First item
2. Second item
3. Third item

- Bullet point 1
- Bullet point 2
- Bullet point 3
