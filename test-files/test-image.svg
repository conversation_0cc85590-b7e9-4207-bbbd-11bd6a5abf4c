<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#f0f8ff" stroke="#4169e1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="200" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    Test SVG Image
  </text>
  
  <!-- Shapes -->
  <circle cx="100" cy="120" r="40" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
  <rect x="160" y="80" width="80" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
  <polygon points="320,80 360,120 340,160 300,160 280,120" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
  
  <!-- Lines -->
  <line x1="50" y1="200" x2="350" y2="200" stroke="#34495e" stroke-width="3"/>
  <line x1="200" y1="180" x2="200" y2="220" stroke="#34495e" stroke-width="3"/>
  
  <!-- Text -->
  <text x="200" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#7f8c8d">
    SVG Preview Test - Shapes, Lines, and Text
  </text>
  
  <!-- Gradient -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <ellipse cx="200" cy="280" rx="150" ry="15" fill="url(#grad1)"/>
</svg>
