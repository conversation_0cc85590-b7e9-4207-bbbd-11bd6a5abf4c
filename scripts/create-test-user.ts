import { db } from '../src/server/db'
import { hashPassword } from '../src/lib/auth'

async function createTestUser() {
  try {
    // Check if test user already exists
    const existingUser = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('Test user already exists:', existingUser)
      return existingUser
    }

    // Hash the password
    const hashedPassword = await hashPassword('test123')

    // Create test user
    const testUser = await db.user.create({
      data: {
        id: 'test-user-id-12345',
        name: 'Test User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'USER',
      }
    })

    console.log('Test user created successfully:', testUser)
    return testUser

  } catch (error) {
    console.error('Error creating test user:', error)
    throw error
  } finally {
    await db.$disconnect()
  }
}

// Run the script
createTestUser()
  .then(() => {
    console.log('<PERSON><PERSON><PERSON> completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('<PERSON><PERSON><PERSON> failed:', error)
    process.exit(1)
  })
