import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'

export interface PaymentStatus {
  orderId: string
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'EXPIRED' | 'REFUNDED'
  amount: number
  currency: string
  paymentMethod: string
  paidAt?: string
  externalId?: string
  events: Array<{
    id: string
    eventType: string
    status: string
    message: string
    createdAt: string
    data?: any
  }>
  providerStatus?: any
}

export interface UsePaymentStatusOptions {
  orderId: string
  enabled?: boolean
  pollInterval?: number
  onStatusChange?: (status: PaymentStatus) => void
  onCompleted?: (status: PaymentStatus) => void
  onFailed?: (status: PaymentStatus) => void
}

export function usePaymentStatus({
  orderId,
  enabled = true,
  pollInterval = 3000, // Poll every 3 seconds
  onStatusChange,
  onCompleted,
  onFailed,
}: UsePaymentStatusOptions) {
  const [previousStatus, setPreviousStatus] = useState<string | null>(null)

  const fetchPaymentStatus = useCallback(async (): Promise<PaymentStatus> => {
    const response = await fetch(`/api/payments/status/${orderId}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to fetch payment status')
    }

    const result = await response.json()
    return result.data
  }, [orderId])

  const {
    data: paymentStatus,
    error,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['payment-status', orderId],
    queryFn: fetchPaymentStatus,
    enabled: enabled && !!orderId,
    refetchInterval: pollInterval,
    refetchIntervalInBackground: true,
    staleTime: 1000, // Consider data stale after 1 second
  })

  // Handle status changes and stop polling for final states
  useEffect(() => {
    if (paymentStatus && paymentStatus.status !== previousStatus) {
      setPreviousStatus(paymentStatus.status)

      // Call status change callback
      onStatusChange?.(paymentStatus)

      // Call specific status callbacks
      if (paymentStatus.status === 'COMPLETED') {
        onCompleted?.(paymentStatus)
      } else if (paymentStatus.status === 'FAILED') {
        onFailed?.(paymentStatus)
      }
    }
  }, [paymentStatus, previousStatus, onStatusChange, onCompleted, onFailed])

  const isCompleted = paymentStatus?.status === 'COMPLETED'
  const isFailed = paymentStatus?.status === 'FAILED'
  const isCancelled = paymentStatus?.status === 'CANCELLED'
  const isPending = paymentStatus?.status === 'PENDING'
  const isProcessing = paymentStatus?.status === 'PROCESSING'
  const isExpired = paymentStatus?.status === 'EXPIRED'
  const isRefunded = paymentStatus?.status === 'REFUNDED'

  const isFinalState = isCompleted || isFailed || isCancelled || isExpired || isRefunded

  return {
    paymentStatus,
    error,
    isLoading,
    isError,
    refetch,
    
    // Status helpers
    isCompleted,
    isFailed,
    isCancelled,
    isPending,
    isProcessing,
    isExpired,
    isRefunded,
    isFinalState,
    
    // Status display helpers
    statusText: getStatusText(paymentStatus?.status),
    statusColor: getStatusColor(paymentStatus?.status),
    
    // Latest event
    latestEvent: paymentStatus?.events?.[0],
  }
}

function getStatusText(status?: string): string {
  switch (status) {
    case 'PENDING':
      return 'Pending Payment'
    case 'PROCESSING':
      return 'Processing Payment'
    case 'COMPLETED':
      return 'Payment Completed'
    case 'FAILED':
      return 'Payment Failed'
    case 'CANCELLED':
      return 'Payment Cancelled'
    case 'EXPIRED':
      return 'Payment Expired'
    case 'REFUNDED':
      return 'Payment Refunded'
    default:
      return 'Unknown Status'
  }
}

function getStatusColor(status?: string): string {
  switch (status) {
    case 'PENDING':
      return 'text-yellow-600'
    case 'PROCESSING':
      return 'text-blue-600'
    case 'COMPLETED':
      return 'text-green-600'
    case 'FAILED':
      return 'text-red-600'
    case 'CANCELLED':
      return 'text-gray-600'
    case 'EXPIRED':
      return 'text-orange-600'
    case 'REFUNDED':
      return 'text-purple-600'
    default:
      return 'text-gray-500'
  }
}
