
import { paymentService } from '@/lib/payments/payment-service'
import {
  createAuthenticatedHandler,
  createSuccessResponse,
  createErrorResponse,
  AuthenticatedRequest
} from '@/lib/middleware/auth'

export const GET = createAuthenticatedHandler(
  async (request: AuthenticatedRequest, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id: paymentId } = await params

      // Get payment
      const payment = await paymentService.getPayment(paymentId)

      if (!payment) {
        return createErrorResponse('Payment not found', 404)
      }

      // Check if user owns the payment
      if (payment.userId !== request.user.id) {
        return createErrorResponse('Forbidden', 403)
      }

      return createSuccessResponse(payment)
    } catch (error) {
      console.error('Get payment API error:', error)
      return createErrorResponse(
        error instanceof Error ? error.message : 'Internal server error',
        500
      )
    }
  }
)
