'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { usePayment } from '@/hooks/use-payment'
import { usePaymentStatus } from '@/hooks/use-payment-status'
import { PaymentMethod } from '@prisma/client'
import { 
  CreditCard, 
  Smartphone, 
  QrCode, 
  Building2, 
  TestTube,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Copy,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { toast } from 'sonner'

interface TestScenario {
  name: string
  amount: number
  currency: string
  description: string
  metadata?: Record<string, any>
}

const TEST_SCENARIOS: TestScenario[] = [
  {
    name: 'Small Amount',
    amount: 50000, // 50,000 VND
    currency: 'VND',
    description: 'Test small payment - Coffee purchase',
  },
  {
    name: 'Medium Amount',
    amount: 500000, // 500,000 VND
    currency: 'VND',
    description: 'Test medium payment - Meal delivery',
  },
  {
    name: 'Large Amount',
    amount: 2000000, // 2,000,000 VND
    currency: 'VND',
    description: 'Test large payment - Electronics purchase',
  },
  {
    name: 'Edge Case - Minimum',
    amount: 1000, // 1,000 VND
    currency: 'VND',
    description: 'Test minimum amount payment',
  },
  {
    name: 'Edge Case - High Value',
    amount: ********, // 10,000,000 VND
    currency: 'VND',
    description: 'Test high value payment',
  },
]

const PAYMENT_METHODS = [
  { id: 'MOMO', name: 'MoMo', icon: Smartphone, color: 'bg-pink-500' },
  { id: 'ZALOPAY', name: 'ZaloPay', icon: Smartphone, color: 'bg-blue-500' },
  { id: 'VNPAY', name: 'VNPay', icon: CreditCard, color: 'bg-red-500' },
  { id: 'VIETQR', name: 'VietQR', icon: QrCode, color: 'bg-green-500' },
]

const VIETNAMESE_BANKS = [
  { id: '970415', name: 'Vietinbank', shortName: 'VTB' },
  { id: '970418', name: 'BIDV', shortName: 'BIDV' },
  { id: '970436', name: 'Vietcombank', shortName: 'VCB' },
  { id: '970422', name: 'Military Bank', shortName: 'MB' },
  { id: '970407', name: 'Techcombank', shortName: 'TCB' },
  { id: '970432', name: 'VPBank', shortName: 'VPB' },
  { id: '970405', name: 'Agribank', shortName: 'AGB' },
  { id: '970448', name: 'OCB', shortName: 'OCB' },
  { id: '970454', name: 'VIB', shortName: 'VIB' },
  { id: '970429', name: 'Sacombank', shortName: 'STB' },
]

export default function PaymentTestPage() {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [selectedScenario, setSelectedScenario] = useState<TestScenario | null>(null)
  const [customAmount, setCustomAmount] = useState('')
  const [customDescription, setCustomDescription] = useState('')
  const [bankId, setBankId] = useState('970415') // Default to Vietinbank
  const [accountNumber, setAccountNumber] = useState('')
  const [accountName, setAccountName] = useState('')
  const [testResults, setTestResults] = useState<any[]>([])
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null)

  const {
    config,
    isConfigLoading,
    createPayment,
    isCreatingPayment,
    createPaymentError,
    formatAmount,
    validatePaymentAmount,
  } = usePayment()

  const {
    paymentStatus,
    isLoading: isStatusLoading,
    refetch: refetchStatus,
  } = usePaymentStatus({
    orderId: currentOrderId || '',
    enabled: !!currentOrderId,
    onStatusChange: (status) => {
      console.log('Payment status changed:', status)
    },
    onCompleted: (status) => {
      toast.success('Payment completed successfully!')
      addTestResult('success', `Payment ${status.orderId} completed`, status)
    },
    onFailed: (status) => {
      toast.error('Payment failed!')
      addTestResult('error', `Payment ${status.orderId} failed`, status)
    },
  })

  const addTestResult = (type: 'success' | 'error' | 'info', message: string, data?: any) => {
    const result = {
      id: Date.now(),
      type,
      message,
      data,
      timestamp: new Date().toISOString(),
    }
    setTestResults(prev => [result, ...prev])
  }

  const handleQuickTest = async (method: PaymentMethod, scenario: TestScenario) => {
    if (!method || !scenario) return

    const validation = validatePaymentAmount(scenario.amount, method)
    if (!validation.valid) {
      toast.error(validation.error)
      addTestResult('error', `Validation failed for ${method}: ${validation.error}`)
      return
    }

    try {
      addTestResult('info', `Starting ${method} test with ${scenario.name}`)

      // Prepare metadata with VietQR bank account info for quick tests
      const metadata: Record<string, any> = {
        testScenario: scenario.name,
        ...scenario.metadata,
      }

      // Add sample bank account info for VietQR quick tests
      if (method === 'VIETQR') {
        metadata.bankId = '970415' // Vietinbank
        metadata.accountNo = '**********'
        metadata.accountName = 'NGUYEN VAN TEST'
      }

      const payment = await createPayment({
        amount: scenario.amount,
        currency: scenario.currency,
        paymentMethod: method,
        description: scenario.description,
        metadata,
      })

      setCurrentOrderId(payment.orderId)
      addTestResult('success', `${method} payment created: ${payment.orderId}`, payment)

      if (payment.paymentUrl) {
        toast.success('Payment URL generated! Check test results for details.')
      }
      if (payment.qrCode) {
        toast.success('QR Code generated! Check test results for details.')
      }

    } catch (error) {
      console.error('Payment test failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`${method} test failed: ${errorMessage}`)
      addTestResult('error', `${method} test failed: ${errorMessage}`, error)
    }
  }

  const handleCustomTest = async () => {
    if (!selectedMethod) {
      toast.error('Please select a payment method')
      return
    }

    const amount = parseInt(customAmount)
    if (!amount || amount <= 0) {
      toast.error('Please enter a valid amount')
      return
    }

    // Validate VietQR specific requirements
    if (selectedMethod === 'VIETQR') {
      if (!accountNumber.trim()) {
        toast.error('Bank account number is required for VietQR')
        return
      }
      if (!accountName.trim()) {
        toast.error('Account holder name is required for VietQR')
        return
      }
      if (accountNumber.length < 6 || accountNumber.length > 20) {
        toast.error('Account number must be between 6-20 digits')
        return
      }
      if (!/^\d+$/.test(accountNumber)) {
        toast.error('Account number must contain only digits')
        return
      }
    }

    const validation = validatePaymentAmount(amount, selectedMethod)
    if (!validation.valid) {
      toast.error(validation.error)
      return
    }

    try {
      addTestResult('info', `Starting custom ${selectedMethod} test`)

      // Prepare metadata based on payment method
      const metadata: Record<string, any> = {
        testType: 'custom',
        customAmount: amount,
      }

      // Add VietQR specific metadata
      if (selectedMethod === 'VIETQR') {
        metadata.bankId = bankId
        metadata.accountNo = accountNumber
        metadata.accountName = accountName
      }

      const payment = await createPayment({
        amount,
        currency: 'VND',
        paymentMethod: selectedMethod,
        description: customDescription || `Custom test payment - ${selectedMethod}`,
        metadata,
      })

      setCurrentOrderId(payment.orderId)
      addTestResult('success', `Custom ${selectedMethod} payment created: ${payment.orderId}`, payment)

      if (payment.paymentUrl) {
        toast.success('Payment URL generated!')
      }
      if (payment.qrCode) {
        toast.success('QR Code generated!')
      }

    } catch (error) {
      console.error('Custom payment test failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`Custom test failed: ${errorMessage}`)
      addTestResult('error', `Custom test failed: ${errorMessage}`, error)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const openPaymentUrl = (url: string) => {
    window.open(url, '_blank')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'PROCESSING':
        return <Clock className="h-4 w-4 text-blue-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-blue-500" />
    }
  }

  return (
    <div className="container mx-auto max-w-7xl px-4 py-8 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <TestTube className="h-8 w-8" />
            Payment Testing Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive testing suite for Vietnamese payment methods
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Testing Controls */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Tests */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Test Scenarios</CardTitle>
                <CardDescription>
                  Run predefined test scenarios for each payment method
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {PAYMENT_METHODS.map((method) => (
                    <div key={method.id} className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className={`p-2 rounded-lg ${method.color}`}>
                          <method.icon className="h-4 w-4 text-white" />
                        </div>
                        <h3 className="font-semibold">{method.name}</h3>
                      </div>
                      <div className="space-y-2">
                        {TEST_SCENARIOS.slice(0, 3).map((scenario) => (
                          <Button
                            key={`${method.id}-${scenario.name}`}
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => handleQuickTest(method.id as PaymentMethod, scenario)}
                            disabled={isCreatingPayment}
                          >
                            {formatAmount(scenario.amount, scenario.currency)} - {scenario.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Custom Test */}
            <Card>
              <CardHeader>
                <CardTitle>Custom Test</CardTitle>
                <CardDescription>
                  Create a custom payment test with your own parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="payment-method">Payment Method</Label>
                    <Select
                      value={selectedMethod || ''}
                      onValueChange={(value) => setSelectedMethod(value as PaymentMethod)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        {PAYMENT_METHODS.map((method) => (
                          <SelectItem key={method.id} value={method.id}>
                            {method.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (VND)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                    />
                  </div>
                </div>

                {/* VietQR Bank Account Fields */}
                {selectedMethod === 'VIETQR' && (
                  <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                    <div className="flex items-center gap-2 mb-2">
                      <Building2 className="h-4 w-4 text-green-600" />
                      <h4 className="font-medium text-sm">VietQR Bank Account Information</h4>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="bank-select">Bank</Label>
                        <Select value={bankId} onValueChange={setBankId}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select bank" />
                          </SelectTrigger>
                          <SelectContent>
                            {VIETNAMESE_BANKS.map((bank) => (
                              <SelectItem key={bank.id} value={bank.id}>
                                {bank.name} ({bank.shortName})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="account-number">Account Number</Label>
                        <Input
                          id="account-number"
                          type="text"
                          placeholder="Enter account number"
                          value={accountNumber}
                          onChange={(e) => setAccountNumber(e.target.value)}
                          maxLength={20}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="account-name">Account Holder Name</Label>
                      <Input
                        id="account-name"
                        type="text"
                        placeholder="Enter account holder name"
                        value={accountName}
                        onChange={(e) => setAccountName(e.target.value)}
                      />
                    </div>
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        For testing purposes, you can use any valid account number format (6-20 digits) and name.
                        The system will validate the format but won't verify actual bank account existence.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Payment description"
                    value={customDescription}
                    onChange={(e) => setCustomDescription(e.target.value)}
                  />
                </div>
                <Button
                  onClick={handleCustomTest}
                  disabled={isCreatingPayment || !selectedMethod}
                  className="w-full"
                >
                  {isCreatingPayment ? 'Creating Payment...' : 'Run Custom Test'}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Status & Results */}
          <div className="space-y-6">
            {/* Current Payment Status */}
            {currentOrderId && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Current Payment
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refetchStatus()}
                      disabled={isStatusLoading}
                    >
                      <RefreshCw className={`h-4 w-4 ${isStatusLoading ? 'animate-spin' : ''}`} />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {paymentStatus ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Order ID</span>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-sm">{paymentStatus.orderId}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(paymentStatus.orderId)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Status</span>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(paymentStatus.status)}
                          <Badge variant="outline">{paymentStatus.status}</Badge>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Amount</span>
                        <span className="font-semibold">
                          {formatAmount(paymentStatus.amount, paymentStatus.currency)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Method</span>
                        <Badge>{paymentStatus.paymentMethod}</Badge>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground">
                      Loading payment status...
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Test Results */}
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  Recent test results and logs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.length === 0 ? (
                    <div className="text-center text-muted-foreground py-4">
                      No test results yet. Run a test to see results here.
                    </div>
                  ) : (
                    testResults.map((result) => (
                      <div key={result.id} className="border rounded-lg p-3 space-y-2">
                        <div className="flex items-start gap-2">
                          {getResultIcon(result.type)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium">{result.message}</p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(result.timestamp).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                        {result.data && (
                          <details className="text-xs">
                            <summary className="cursor-pointer text-muted-foreground">
                              View details
                            </summary>
                            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                        {result.data?.paymentUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openPaymentUrl(result.data.paymentUrl)}
                            className="w-full"
                          >
                            <ExternalLink className="h-3 w-3 mr-2" />
                            Open Payment URL
                          </Button>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
