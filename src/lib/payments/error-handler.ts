import { PaymentMethod } from '@prisma/client'

export interface PaymentError {
  code: string
  message: string
  provider: string
  retryable: boolean
  userMessage: string
  details?: any
}

export class PaymentErrorHandler {
  /**
   * Handle MoMo errors
   */
  static handleMoMoError(error: any): PaymentError {
    const resultCode = error.resultCode || error.response?.data?.resultCode

    switch (resultCode) {
      case 9000:
        return {
          code: 'MOMO_USER_REJECTED',
          message: 'Transaction was rejected by user',
          provider: 'momo',
          retryable: true,
          userMessage: 'Payment was cancelled. You can try again.',
          details: error,
        }
      case 8000:
        return {
          code: 'MOMO_TIMEOUT',
          message: 'Transaction timeout',
          provider: 'momo',
          retryable: true,
          userMessage: 'Payment timed out. Please try again.',
          details: error,
        }
      case 7000:
        return {
          code: 'MOMO_SYSTEM_REJECTED',
          message: 'Transaction was rejected by system',
          provider: 'momo',
          retryable: false,
          userMessage: 'Payment was rejected. Please contact support.',
          details: error,
        }
      case 1000:
        return {
          code: 'MOMO_ISSUER_REJECTED',
          message: 'Transaction was rejected by issuer',
          provider: 'momo',
          retryable: false,
          userMessage: 'Payment was declined by your bank. Please try a different payment method.',
          details: error,
        }
      default:
        return {
          code: 'MOMO_UNKNOWN_ERROR',
          message: `MoMo error: ${error.message || 'Unknown error'}`,
          provider: 'momo',
          retryable: true,
          userMessage: 'Payment failed. Please try again.',
          details: error,
        }
    }
  }

  /**
   * Handle ZaloPay errors
   */
  static handleZaloPayError(error: any): PaymentError {
    const returnCode = error.return_code || error.response?.data?.return_code

    switch (returnCode) {
      case -1:
        return {
          code: 'ZALOPAY_INVALID_PARAMS',
          message: 'Invalid parameters',
          provider: 'zalopay',
          retryable: false,
          userMessage: 'Invalid payment information. Please contact support.',
          details: error,
        }
      case -2:
        return {
          code: 'ZALOPAY_INVALID_MAC',
          message: 'Invalid MAC',
          provider: 'zalopay',
          retryable: false,
          userMessage: 'Payment verification failed. Please contact support.',
          details: error,
        }
      case -3:
        return {
          code: 'ZALOPAY_APP_NOT_FOUND',
          message: 'App not found',
          provider: 'zalopay',
          retryable: false,
          userMessage: 'Payment service unavailable. Please contact support.',
          details: error,
        }
      default:
        return {
          code: 'ZALOPAY_UNKNOWN_ERROR',
          message: `ZaloPay error: ${error.message || 'Unknown error'}`,
          provider: 'zalopay',
          retryable: true,
          userMessage: 'Payment failed. Please try again.',
          details: error,
        }
    }
  }

  /**
   * Handle VNPay errors
   */
  static handleVNPayError(error: any): PaymentError {
    const responseCode = error.vnp_ResponseCode || error.response?.data?.vnp_ResponseCode

    switch (responseCode) {
      case '07':
        return {
          code: 'VNPAY_FRAUD_SUSPECTED',
          message: 'Transaction suspected of fraud',
          provider: 'vnpay',
          retryable: false,
          userMessage: 'Payment was flagged for security review. Please contact support.',
          details: error,
        }
      case '09':
        return {
          code: 'VNPAY_USER_CANCELLED',
          message: 'Customer cancelled transaction',
          provider: 'vnpay',
          retryable: true,
          userMessage: 'Payment was cancelled. You can try again.',
          details: error,
        }
      case '10':
        return {
          code: 'VNPAY_TOO_MANY_ATTEMPTS',
          message: 'Too many failed attempts',
          provider: 'vnpay',
          retryable: false,
          userMessage: 'Too many failed attempts. Please try again later.',
          details: error,
        }
      case '11':
        return {
          code: 'VNPAY_EXPIRED',
          message: 'Payment deadline expired',
          provider: 'vnpay',
          retryable: true,
          userMessage: 'Payment expired. Please try again.',
          details: error,
        }
      case '51':
        return {
          code: 'VNPAY_INSUFFICIENT_FUNDS',
          message: 'Insufficient balance',
          provider: 'vnpay',
          retryable: true,
          userMessage: 'Insufficient funds. Please check your account balance.',
          details: error,
        }
      default:
        return {
          code: 'VNPAY_UNKNOWN_ERROR',
          message: `VNPay error: ${error.message || 'Unknown error'}`,
          provider: 'vnpay',
          retryable: true,
          userMessage: 'Payment failed. Please try again.',
          details: error,
        }
    }
  }

  /**
   * Handle Stripe errors
   */
  static handleStripeError(error: any): PaymentError {
    const stripeError = error.type || error.code

    switch (stripeError) {
      case 'card_error':
        return {
          code: 'STRIPE_CARD_ERROR',
          message: error.message || 'Card error',
          provider: 'stripe',
          retryable: true,
          userMessage: error.message || 'Card payment failed. Please check your card details.',
          details: error,
        }
      case 'rate_limit_error':
        return {
          code: 'STRIPE_RATE_LIMIT',
          message: 'Too many requests',
          provider: 'stripe',
          retryable: true,
          userMessage: 'Service temporarily busy. Please try again in a moment.',
          details: error,
        }
      case 'invalid_request_error':
        return {
          code: 'STRIPE_INVALID_REQUEST',
          message: error.message || 'Invalid request',
          provider: 'stripe',
          retryable: false,
          userMessage: 'Invalid payment information. Please contact support.',
          details: error,
        }
      case 'authentication_error':
        return {
          code: 'STRIPE_AUTH_ERROR',
          message: 'Authentication failed',
          provider: 'stripe',
          retryable: false,
          userMessage: 'Payment service authentication failed. Please contact support.',
          details: error,
        }
      case 'api_connection_error':
        return {
          code: 'STRIPE_CONNECTION_ERROR',
          message: 'Network error',
          provider: 'stripe',
          retryable: true,
          userMessage: 'Connection error. Please check your internet and try again.',
          details: error,
        }
      default:
        return {
          code: 'STRIPE_UNKNOWN_ERROR',
          message: `Stripe error: ${error.message || 'Unknown error'}`,
          provider: 'stripe',
          retryable: true,
          userMessage: 'Payment failed. Please try again.',
          details: error,
        }
    }
  }

  /**
   * Handle PayPal errors
   */
  static handlePayPalError(error: any): PaymentError {
    const errorName = error.name || error.response?.data?.name

    switch (errorName) {
      case 'INSTRUMENT_DECLINED':
        return {
          code: 'PAYPAL_INSTRUMENT_DECLINED',
          message: 'Payment method declined',
          provider: 'paypal',
          retryable: true,
          userMessage: 'Payment method was declined. Please try a different payment method.',
          details: error,
        }
      case 'PAYER_ACTION_REQUIRED':
        return {
          code: 'PAYPAL_ACTION_REQUIRED',
          message: 'Payer action required',
          provider: 'paypal',
          retryable: true,
          userMessage: 'Additional verification required. Please complete the payment process.',
          details: error,
        }
      case 'INVALID_REQUEST':
        return {
          code: 'PAYPAL_INVALID_REQUEST',
          message: 'Invalid request',
          provider: 'paypal',
          retryable: false,
          userMessage: 'Invalid payment information. Please contact support.',
          details: error,
        }
      case 'AUTHENTICATION_FAILURE':
        return {
          code: 'PAYPAL_AUTH_FAILURE',
          message: 'Authentication failed',
          provider: 'paypal',
          retryable: false,
          userMessage: 'Payment service authentication failed. Please contact support.',
          details: error,
        }
      default:
        return {
          code: 'PAYPAL_UNKNOWN_ERROR',
          message: `PayPal error: ${error.message || 'Unknown error'}`,
          provider: 'paypal',
          retryable: true,
          userMessage: 'Payment failed. Please try again.',
          details: error,
        }
    }
  }

  /**
   * Handle generic errors
   */
  static handleGenericError(error: any, provider: string): PaymentError {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed',
        provider,
        retryable: true,
        userMessage: 'Connection error. Please check your internet and try again.',
        details: error,
      }
    }

    if (error.code === 'ETIMEDOUT') {
      return {
        code: 'TIMEOUT_ERROR',
        message: 'Request timeout',
        provider,
        retryable: true,
        userMessage: 'Request timed out. Please try again.',
        details: error,
      }
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'Unknown error occurred',
      provider,
      retryable: true,
      userMessage: 'An unexpected error occurred. Please try again.',
      details: error,
    }
  }

  /**
   * Main error handler
   */
  static handleError(error: any, paymentMethod: PaymentMethod): PaymentError {
    const provider = paymentMethod.toLowerCase()

    switch (paymentMethod) {
      case PaymentMethod.MOMO:
        return this.handleMoMoError(error)
      case PaymentMethod.ZALOPAY:
        return this.handleZaloPayError(error)
      case PaymentMethod.VNPAY:
        return this.handleVNPayError(error)
      case PaymentMethod.STRIPE:
        return this.handleStripeError(error)
      case PaymentMethod.PAYPAL:
        return this.handlePayPalError(error)
      default:
        return this.handleGenericError(error, provider)
    }
  }
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> {
  let lastError: any
  
  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      
      // Don't retry on last attempt
      if (attempt === config.maxAttempts) {
        break
      }
      
      // Check if error is retryable
      const paymentError = error as PaymentError
      if (paymentError.retryable === false) {
        break
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
        config.maxDelay
      )
      
      // Note: Using console.log here for retry logging is acceptable in development
      // In production, consider using a proper logging service
      if (process.env.NODE_ENV === 'development') {
        console.log(`Retry attempt ${attempt} failed, retrying in ${delay}ms:`, error instanceof Error ? error.message : String(error))
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}
