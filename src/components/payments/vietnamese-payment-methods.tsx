'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  CheckCircle,
  Loader2,
  Building2,
  AlertCircle,
} from 'lucide-react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { usePayment } from '@/hooks/use-payment'
import { usePaymentStatus } from '@/hooks/use-payment-status'
import { PaymentMethod } from '@prisma/client'
import { PaymentMethodLogos } from './payment-icons'

interface VietnamesePaymentMethodsProps {
  selectedMethod: string | null
  onMethodSelect: (method: string) => void
  amount: number
  currency: string
  description?: string
}

type PaymentStep = 'select' | 'form' | 'processing' | 'success' | 'error'

// Validation schemas
const phoneSchema = z.object({
  phoneNumber: z.string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(11, 'Phone number must be at most 11 digits')
    .regex(/^[0-9]+$/, 'Phone number must contain only digits'),
})

const bankSchema = z.object({
  bankId: z.string().min(1, 'Please select a bank'),
  accountNumber: z.string()
    .min(6, 'Account number must be at least 6 digits')
    .max(20, 'Account number must be at most 20 digits')
    .regex(/^[0-9]+$/, 'Account number must contain only digits'),
  accountName: z.string().min(2, 'Account name is required'),
})

export function VietnamesePaymentMethods({
  selectedMethod,
  onMethodSelect,
  amount,
  currency,
  description,
}: VietnamesePaymentMethodsProps) {
  const [paymentStep, setPaymentStep] = useState<PaymentStep>('select')
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null)
  
  const {
    config,
    isConfigLoading,
    createPayment,
    isCreatingPayment,
    createPaymentError,
    formatAmount,
    validatePaymentAmount,
    calculateFees,
    redirectToPayment,
  } = usePayment()

  const {
    paymentStatus,
    statusText,
  } = usePaymentStatus({
    orderId: '', // Will be set after payment creation
    enabled: paymentStep === 'processing',
    onCompleted: () => setPaymentStep('success'),
    onFailed: () => setPaymentStep('error'),
  })

  const phoneForm = useForm({
    resolver: zodResolver(phoneSchema),
    defaultValues: { phoneNumber: '' },
  })

  const bankForm = useForm({
    resolver: zodResolver(bankSchema),
    defaultValues: { bankId: '', accountNumber: '', accountName: '' },
  })

  if (isConfigLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading payment methods...</span>
      </div>
    )
  }

  const vietnameseMethods = config?.paymentMethods.vietnamese || []

  const handleMethodSelect = (methodId: string) => {
    onMethodSelect(methodId)
    setPaymentStep('form')
  }

  const handleBack = () => {
    if (paymentStep === 'form') {
      setPaymentStep('select')
      onMethodSelect('')
    }
  }

  const handlePhonePayment = async (data: z.infer<typeof phoneSchema>) => {
    if (!selectedMethod) return

    const validation = validatePaymentAmount(amount, selectedMethod as PaymentMethod)
    if (!validation.valid) {
      phoneForm.setError('root', { message: validation.error })
      return
    }

    try {
      setPaymentStep('processing')
      
      const payment = await createPayment({
        amount,
        currency,
        paymentMethod: selectedMethod as PaymentMethod,
        description,
        metadata: {
          phoneNumber: data.phoneNumber,
        },
      })

      if (payment.paymentUrl) {
        redirectToPayment(payment.paymentUrl)
      } else if (payment.qrCode) {
        setQrCodeUrl(payment.qrCode)
      }
    } catch (error) {
      console.error('Payment creation failed:', error)
      setPaymentStep('error')
    }
  }

  const handleBankPayment = async (data: z.infer<typeof bankSchema>) => {
    if (!selectedMethod) return

    const validation = validatePaymentAmount(amount, selectedMethod as PaymentMethod)
    if (!validation.valid) {
      bankForm.setError('root', { message: validation.error })
      return
    }

    try {
      setPaymentStep('processing')
      
      const payment = await createPayment({
        amount,
        currency,
        paymentMethod: selectedMethod as PaymentMethod,
        description,
        metadata: {
          bankId: data.bankId,
          accountNumber: data.accountNumber,
          accountName: data.accountName,
        },
      })

      if (payment.paymentUrl) {
        redirectToPayment(payment.paymentUrl)
      } else if (payment.qrCode) {
        setQrCodeUrl(payment.qrCode)
      }
    } catch (error) {
      console.error('Payment creation failed:', error)
      setPaymentStep('error')
    }
  }

  if (paymentStep === 'select') {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-green-100 rounded-lg">
            <Building2 className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Vietnamese Payment Methods</h3>
            <p className="text-sm text-muted-foreground">
              Popular payment methods in Vietnam with instant processing
            </p>
          </div>
        </div>

        <div className="grid gap-4">
          {vietnameseMethods.map((method) => {
            const fees = calculateFees(amount, method.id as PaymentMethod)
            const total = amount + fees

            return (
              <motion.div
                key={method.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="cursor-pointer"
                onClick={() => handleMethodSelect(method.id)}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-50 rounded-lg">
                          {PaymentMethodLogos[method.id as keyof typeof PaymentMethodLogos]({ size: 'md' })}
                        </div>
                        <div>
                          <h3 className="font-semibold">{method.name}</h3>
                          <p className="text-sm text-muted-foreground">{method.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">{method.processingTime}</Badge>
                        <p className="text-sm text-muted-foreground mt-1">
                          Fee: {method.fees.percentage}%{method.fees.fixed > 0 && ` + ${formatAmount(method.fees.fixed, currency)}`}
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t">
                      <div className="flex justify-between text-sm">
                        <span>Amount:</span>
                        <span>{formatAmount(amount, currency)}</span>
                      </div>
                      {fees > 0 && (
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Fee:</span>
                          <span>{formatAmount(fees, currency)}</span>
                        </div>
                      )}
                      <Separator className="my-2" />
                      <div className="flex justify-between font-semibold">
                        <span>Total:</span>
                        <span>{formatAmount(total, currency)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>
    )
  }

  if (paymentStep === 'form') {
    const selectedMethodConfig = vietnameseMethods.find(m => m.id === selectedMethod)
    const isPhoneMethod = selectedMethod === 'MOMO' || selectedMethod === 'ZALOPAY'
    const isBankMethod = selectedMethod === 'VNPAY' || selectedMethod === 'VIETQR'

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-3">
            {selectedMethodConfig && PaymentMethodLogos[selectedMethod as keyof typeof PaymentMethodLogos]({ size: 'md' })}
            <div>
              <h3 className="font-semibold">{selectedMethodConfig?.name}</h3>
              <p className="text-sm text-muted-foreground">{selectedMethodConfig?.type}</p>
            </div>
          </div>
        </div>

        {createPaymentError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {createPaymentError.message}
            </AlertDescription>
          </Alert>
        )}

        {isPhoneMethod && (
          <form onSubmit={phoneForm.handleSubmit(handlePhonePayment)} className="space-y-4">
            <div>
              <Label htmlFor="phoneNumber">Phone Number</Label>
              <Input
                id="phoneNumber"
                placeholder="Enter your phone number"
                {...phoneForm.register('phoneNumber')}
              />
              {phoneForm.formState.errors.phoneNumber && (
                <p className="text-sm text-red-600 mt-1">
                  {phoneForm.formState.errors.phoneNumber.message}
                </p>
              )}
              <p className="text-sm text-muted-foreground mt-1">
                Enter your {selectedMethodConfig?.name} registered phone number
              </p>
            </div>

            <Button type="submit" className="w-full" disabled={isCreatingPayment}>
              {isCreatingPayment && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              Pay with {selectedMethodConfig?.name}
            </Button>
          </form>
        )}

        {isBankMethod && (
          <form onSubmit={bankForm.handleSubmit(handleBankPayment)} className="space-y-4">
            {selectedMethod === 'VIETQR' && (
              <div>
                <Label htmlFor="bankId">Bank</Label>
                <Select onValueChange={(value) => bankForm.setValue('bankId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your bank" />
                  </SelectTrigger>
                  <SelectContent>
                    {config?.vietqr.banks.map((bank) => (
                      <SelectItem key={bank.id} value={bank.id}>
                        {bank.name} ({bank.shortName})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {bankForm.formState.errors.bankId && (
                  <p className="text-sm text-red-600 mt-1">
                    {bankForm.formState.errors.bankId.message}
                  </p>
                )}
              </div>
            )}

            <div>
              <Label htmlFor="accountNumber">Account Number</Label>
              <Input
                id="accountNumber"
                placeholder="Enter your account number"
                {...bankForm.register('accountNumber')}
              />
              {bankForm.formState.errors.accountNumber && (
                <p className="text-sm text-red-600 mt-1">
                  {bankForm.formState.errors.accountNumber.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="accountName">Account Name</Label>
              <Input
                id="accountName"
                placeholder="Enter account holder name"
                {...bankForm.register('accountName')}
              />
              {bankForm.formState.errors.accountName && (
                <p className="text-sm text-red-600 mt-1">
                  {bankForm.formState.errors.accountName.message}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={isCreatingPayment}>
              {isCreatingPayment && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              Pay with {selectedMethodConfig?.name}
            </Button>
          </form>
        )}
      </div>
    )
  }

  if (paymentStep === 'processing') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Processing Payment</h3>
          <p className="text-muted-foreground">
            {statusText || 'Please wait while we process your payment...'}
          </p>
        </div>
        
        {qrCodeUrl && (
          <div className="mt-6">
            <p className="text-sm text-muted-foreground mb-4">
              Scan this QR code with your banking app to complete the payment:
            </p>
            <div className="flex justify-center">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img src={qrCodeUrl} alt="Payment QR Code" className="max-w-xs" />
            </div>
          </div>
        )}
      </div>
    )
  }

  if (paymentStep === 'success') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-green-600">Payment Successful!</h3>
          <p className="text-muted-foreground">
            Your payment has been processed successfully.
          </p>
        </div>
        {paymentStatus && (
          <div className="mt-4 p-4 bg-green-50 rounded-lg">
            <p className="text-sm">
              <strong>Transaction ID:</strong> {paymentStatus.externalId}
            </p>
            <p className="text-sm">
              <strong>Amount:</strong> {formatAmount(paymentStatus.amount, paymentStatus.currency)}
            </p>
          </div>
        )}
      </div>
    )
  }

  if (paymentStep === 'error') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <AlertCircle className="h-12 w-12 text-red-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-red-600">Payment Failed</h3>
          <p className="text-muted-foreground">
            {createPaymentError?.message || 'An error occurred while processing your payment.'}
          </p>
        </div>
        <Button onClick={() => setPaymentStep('form')} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return null
}
