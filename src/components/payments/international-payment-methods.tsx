'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  CheckCircle,
  Loader2,
  CreditCard,
  Globe,
  AlertCircle,
  ExternalLink,
} from 'lucide-react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { usePayment } from '@/hooks/use-payment'
import { usePaymentStatus } from '@/hooks/use-payment-status'
import { PaymentMethod } from '@prisma/client'
import { PaymentMethodLogos } from './payment-icons'

interface InternationalPaymentMethodsProps {
  selectedMethod: string | null
  onMethodSelect: (method: string) => void
  amount: number
  currency: string
  description?: string
}

type PaymentStep = 'select' | 'form' | 'processing' | 'success' | 'error'

// Validation schemas
const paypalSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

const stripeSchema = z.object({
  cardNumber: z.string()
    .min(13, 'Card number must be at least 13 digits')
    .max(19, 'Card number must be at most 19 digits')
    .transform((val) => val.replace(/\s/g, ''))
    .refine((val) => {
      // Basic Luhn algorithm validation
      let sum = 0
      let alternate = false
      for (let i = val.length - 1; i >= 0; i--) {
        let n = parseInt(val.charAt(i), 10)
        if (alternate) {
          n *= 2
          if (n > 9) n = (n % 10) + 1
        }
        sum += n
        alternate = !alternate
      }
      return sum % 10 === 0
    }, 'Invalid card number'),
  expiryDate: z.string()
    .regex(/^(0[1-9]|1[0-2])\/\d{2}$/, 'Expiry date must be in MM/YY format')
    .refine((val) => {
      const [month, year] = val.split('/')
      const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1)
      return expiry > new Date()
    }, 'Card has expired'),
  cvv: z.string()
    .min(3, 'CVV must be 3 digits')
    .max(4, 'CVV must be 3-4 digits')
    .regex(/^\d+$/, 'CVV must contain only digits'),
  cardholderName: z.string().min(2, 'Cardholder name is required'),
  address: z.string().min(5, 'Address is required'),
  city: z.string().min(2, 'City is required'),
  postalCode: z.string().min(3, 'Postal code is required'),
  country: z.string().min(1, 'Please select a country'),
})

export function InternationalPaymentMethods({
  selectedMethod,
  onMethodSelect,
  amount,
  currency,
  description,
}: InternationalPaymentMethodsProps) {
  const [paymentStep, setPaymentStep] = useState<PaymentStep>('select')
  const [stripeClientSecret, setStripeClientSecret] = useState<string | null>(null)
  
  const {
    config,
    isConfigLoading,
    createPayment,
    isCreatingPayment,
    createPaymentError,
    formatAmount,
    validatePaymentAmount,
    calculateFees,
    redirectToPayment,
  } = usePayment()

  const {
    paymentStatus,
    statusText,
  } = usePaymentStatus({
    orderId: '', // Will be set after payment creation
    enabled: paymentStep === 'processing',
    onCompleted: () => setPaymentStep('success'),
    onFailed: () => setPaymentStep('error'),
  })

  const paypalForm = useForm({
    resolver: zodResolver(paypalSchema),
    defaultValues: { email: '', password: '' },
  })

  const stripeForm = useForm({
    resolver: zodResolver(stripeSchema),
    defaultValues: {
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardholderName: '',
      address: '',
      city: '',
      postalCode: '',
      country: '',
    },
  })

  if (isConfigLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading payment methods...</span>
      </div>
    )
  }

  const internationalMethods = config?.paymentMethods.international || []

  const handleMethodSelect = (methodId: string) => {
    onMethodSelect(methodId)
    setPaymentStep('form')
  }

  const handleBack = () => {
    if (paymentStep === 'form') {
      setPaymentStep('select')
      onMethodSelect('')
    }
  }

  const handlePayPalPayment = async (data: z.infer<typeof paypalSchema>) => {
    if (!selectedMethod) return

    const validation = validatePaymentAmount(amount, selectedMethod as PaymentMethod)
    if (!validation.valid) {
      paypalForm.setError('root', { message: validation.error })
      return
    }

    try {
      setPaymentStep('processing')
      
      const payment = await createPayment({
        amount,
        currency,
        paymentMethod: selectedMethod as PaymentMethod,
        description,
        metadata: {
          email: data.email,
        },
      })

      if (payment.paymentUrl) {
        redirectToPayment(payment.paymentUrl)
      }
    } catch (error) {
      console.error('PayPal payment creation failed:', error)
      setPaymentStep('error')
    }
  }

  const handleStripePayment = async (data: z.infer<typeof stripeSchema>) => {
    if (!selectedMethod) return

    const validation = validatePaymentAmount(amount, selectedMethod as PaymentMethod)
    if (!validation.valid) {
      stripeForm.setError('root', { message: validation.error })
      return
    }

    try {
      setPaymentStep('processing')
      
      const payment = await createPayment({
        amount,
        currency,
        paymentMethod: selectedMethod as PaymentMethod,
        description,
        metadata: {
          cardholderName: data.cardholderName,
          address: data.address,
          city: data.city,
          postalCode: data.postalCode,
          country: data.country,
        },
      })

      if (payment.clientSecret) {
        setStripeClientSecret(payment.clientSecret)
        // In a real implementation, you would use Stripe Elements here
        // For now, we'll simulate the payment process
        setTimeout(() => {
          setPaymentStep('success')
        }, 3000)
      }
    } catch (error) {
      console.error('Stripe payment creation failed:', error)
      setPaymentStep('error')
    }
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    const matches = v.match(/\d{4,16}/g)
    const match = matches && matches[0] || ''
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(' ')
    } else {
      return v
    }
  }

  if (paymentStep === 'select') {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Globe className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">International Payment Methods</h3>
            <p className="text-sm text-muted-foreground">
              Secure global payment options with worldwide acceptance
            </p>
          </div>
        </div>

        <div className="grid gap-4">
          {internationalMethods.map((method) => {
            const fees = calculateFees(amount, method.id as PaymentMethod)
            const total = amount + fees

            return (
              <motion.div
                key={method.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="cursor-pointer"
                onClick={() => handleMethodSelect(method.id)}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-50 rounded-lg">
                          {PaymentMethodLogos[method.id as keyof typeof PaymentMethodLogos]({ size: 'md' })}
                        </div>
                        <div>
                          <h3 className="font-semibold">{method.name}</h3>
                          <p className="text-sm text-muted-foreground">{method.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">{method.processingTime}</Badge>
                        <p className="text-sm text-muted-foreground mt-1">
                          Fee: {method.fees.percentage}%{method.fees.fixed > 0 && ` + ${formatAmount(method.fees.fixed, currency)}`}
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t">
                      <div className="flex justify-between text-sm">
                        <span>Amount:</span>
                        <span>{formatAmount(amount, currency)}</span>
                      </div>
                      {fees > 0 && (
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Fee:</span>
                          <span>{formatAmount(fees, currency)}</span>
                        </div>
                      )}
                      <Separator className="my-2" />
                      <div className="flex justify-between font-semibold">
                        <span>Total:</span>
                        <span>{formatAmount(total, currency)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>
    )
  }

  if (paymentStep === 'form') {
    const selectedMethodConfig = internationalMethods.find(m => m.id === selectedMethod)

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-3">
            {selectedMethodConfig && PaymentMethodLogos[selectedMethod as keyof typeof PaymentMethodLogos]({ size: 'md' })}
            <div>
              <h3 className="font-semibold">{selectedMethodConfig?.name}</h3>
              <p className="text-sm text-muted-foreground">{selectedMethodConfig?.type}</p>
            </div>
          </div>
        </div>

        {createPaymentError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {createPaymentError.message}
            </AlertDescription>
          </Alert>
        )}

        {selectedMethod === 'PAYPAL' && (
          <form onSubmit={paypalForm.handleSubmit(handlePayPalPayment)} className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 text-blue-700">
                <Globe className="h-4 w-4" />
                <span className="text-sm font-medium">Secure PayPal Login</span>
              </div>
              <p className="text-sm text-blue-600 mt-1">
                You&apos;ll be redirected to PayPal to complete your payment securely.
              </p>
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your PayPal email"
                {...paypalForm.register('email')}
              />
              {paypalForm.formState.errors.email && (
                <p className="text-sm text-red-600 mt-1">
                  {paypalForm.formState.errors.email.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your PayPal password"
                {...paypalForm.register('password')}
              />
              {paypalForm.formState.errors.password && (
                <p className="text-sm text-red-600 mt-1">
                  {paypalForm.formState.errors.password.message}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={isCreatingPayment}>
              {isCreatingPayment && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              Continue with PayPal
              <ExternalLink className="h-4 w-4 ml-2" />
            </Button>
          </form>
        )}

        {selectedMethod === 'STRIPE' && (
          <form onSubmit={stripeForm.handleSubmit(handleStripePayment)} className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 text-green-700">
                <CreditCard className="h-4 w-4" />
                <span className="text-sm font-medium">Secure Card Processing</span>
              </div>
              <p className="text-sm text-green-600 mt-1">
                Your card information is encrypted and processed securely by Stripe.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  {...stripeForm.register('cardNumber')}
                  onChange={(e) => {
                    const formatted = formatCardNumber(e.target.value)
                    e.target.value = formatted
                    stripeForm.setValue('cardNumber', formatted)
                  }}
                />
                {stripeForm.formState.errors.cardNumber && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.cardNumber.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Enter your 16-digit card number
                </p>
              </div>

              <div>
                <Label htmlFor="expiryDate">Expiry Date</Label>
                <Input
                  id="expiryDate"
                  placeholder="MM/YY"
                  {...stripeForm.register('expiryDate')}
                />
                {stripeForm.formState.errors.expiryDate && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.expiryDate.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Card expiration date
                </p>
              </div>

              <div>
                <Label htmlFor="cvv">CVV</Label>
                <Input
                  id="cvv"
                  placeholder="123"
                  maxLength={4}
                  {...stripeForm.register('cvv')}
                />
                {stripeForm.formState.errors.cvv && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.cvv.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  3-digit security code
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="cardholderName">Cardholder Name</Label>
              <Input
                id="cardholderName"
                placeholder="John Doe"
                {...stripeForm.register('cardholderName')}
              />
              {stripeForm.formState.errors.cardholderName && (
                <p className="text-sm text-red-600 mt-1">
                  {stripeForm.formState.errors.cardholderName.message}
                </p>
              )}
              <p className="text-sm text-muted-foreground mt-1">
                Name as it appears on your card
              </p>
            </div>

            <h4 className="font-semibold">Billing Address</h4>

            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                placeholder="123 Main Street"
                {...stripeForm.register('address')}
              />
              {stripeForm.formState.errors.address && (
                <p className="text-sm text-red-600 mt-1">
                  {stripeForm.formState.errors.address.message}
                </p>
              )}
              <p className="text-sm text-muted-foreground mt-1">
                Your billing address
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="New York"
                  {...stripeForm.register('city')}
                />
                {stripeForm.formState.errors.city && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.city.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  placeholder="10001"
                  {...stripeForm.register('postalCode')}
                />
                {stripeForm.formState.errors.postalCode && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.postalCode.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="country">Country</Label>
                <Select onValueChange={(value) => stripeForm.setValue('country', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="US">United States</SelectItem>
                    <SelectItem value="CA">Canada</SelectItem>
                    <SelectItem value="GB">United Kingdom</SelectItem>
                    <SelectItem value="AU">Australia</SelectItem>
                    <SelectItem value="VN">Vietnam</SelectItem>
                    <SelectItem value="SG">Singapore</SelectItem>
                    <SelectItem value="JP">Japan</SelectItem>
                    <SelectItem value="KR">South Korea</SelectItem>
                  </SelectContent>
                </Select>
                {stripeForm.formState.errors.country && (
                  <p className="text-sm text-red-600 mt-1">
                    {stripeForm.formState.errors.country.message}
                  </p>
                )}
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isCreatingPayment}>
              {isCreatingPayment && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              Pay with Card
            </Button>
          </form>
        )}
      </div>
    )
  }

  if (paymentStep === 'processing') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Processing Payment</h3>
          <p className="text-muted-foreground">
            {statusText || 'Please wait while we process your payment...'}
          </p>
        </div>
        
        {stripeClientSecret && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-700">
              Payment is being processed securely by Stripe...
            </p>
          </div>
        )}
      </div>
    )
  }

  if (paymentStep === 'success') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-green-600">Payment Successful!</h3>
          <p className="text-muted-foreground">
            Your payment has been processed successfully.
          </p>
        </div>
        {paymentStatus && (
          <div className="mt-4 p-4 bg-green-50 rounded-lg">
            <p className="text-sm">
              <strong>Transaction ID:</strong> {paymentStatus.externalId}
            </p>
            <p className="text-sm">
              <strong>Amount:</strong> {formatAmount(paymentStatus.amount, paymentStatus.currency)}
            </p>
          </div>
        )}
      </div>
    )
  }

  if (paymentStep === 'error') {
    return (
      <div className="text-center space-y-4 py-8">
        <div className="flex justify-center">
          <AlertCircle className="h-12 w-12 text-red-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-red-600">Payment Failed</h3>
          <p className="text-muted-foreground">
            {createPaymentError?.message || 'An error occurred while processing your payment.'}
          </p>
        </div>
        <Button onClick={() => setPaymentStep('form')} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return null
}
